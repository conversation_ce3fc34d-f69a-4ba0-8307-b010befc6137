/**
 * Validation Utilities for Trading Bot
 * Provides comprehensive validation functions for trading bot form inputs
 */

import type {
  TradingBotFormData,
  TradingBotFormErrors,
  TradeExpiryDuration
} from '../types/trading'
import { VALIDATION_CONSTRAINTS, ERROR_MESSAGES } from '../constants/trading'

/**
 * Validates a numeric string value
 */
export const validateNumericValue = (
  value: string,
  fieldName: keyof typeof VALIDATION_CONSTRAINTS,
  additionalConstraints?: {
    maxValue?: number
    customErrorMessages?: Partial<(typeof ERROR_MESSAGES)[keyof typeof ERROR_MESSAGES]>
  }
): string | undefined => {
  // Check if value is empty
  if (!value || !value.trim()) {
    return ERROR_MESSAGES[fieldName].required
  }

  // Parse the numeric value
  const numValue = parseFloat(value.trim())

  // Check if it's a valid number
  if (isNaN(numValue)) {
    return ERROR_MESSAGES[fieldName].invalid
  }

  // Get validation constraints
  const constraints = VALIDATION_CONSTRAINTS[fieldName]

  // Check minimum value
  if (numValue < constraints.min) {
    return ERROR_MESSAGES[fieldName].tooLow
  }

  // Check maximum value
  const maxValue = additionalConstraints?.maxValue ?? constraints.max
  if (numValue > maxValue) {
    return ERROR_MESSAGES[fieldName].tooHigh
  }

  return undefined
}

/**
 * Validates trade capital
 */
export const validateTradeCapital = (value: string): string | undefined => {
  return validateNumericValue(value, 'tradeCapital')
}

/**
 * Validates target profit
 */
export const validateTargetProfit = (value: string): string | undefined => {
  return validateNumericValue(value, 'targetProfit')
}

/**
 * Validates trade amount with additional check against trade capital
 */
export const validateTradeAmount = (value: string, tradeCapital?: string): string | undefined => {
  const basicValidation = validateNumericValue(value, 'tradeAmount')
  if (basicValidation) {
    return basicValidation
  }

  // Additional validation: trade amount should not exceed trade capital
  if (tradeCapital) {
    const tradeCapitalNum = parseFloat(tradeCapital)
    const tradeAmountNum = parseFloat(value)

    if (!isNaN(tradeCapitalNum) && !isNaN(tradeAmountNum) && tradeAmountNum > tradeCapitalNum) {
      return ERROR_MESSAGES.tradeAmount.exceedsCapital
    }
  }

  return undefined
}

/**
 * Validates trade expiry duration
 */
export const validateTradeExpiryDuration = (
  duration: TradeExpiryDuration | null
): string | undefined => {
  if (!duration) {
    return ERROR_MESSAGES.tradeExpiryDuration.required
  }
  return undefined
}

/**
 * Validates the entire trading bot form
 */
export const validateTradingBotForm = (formData: TradingBotFormData): TradingBotFormErrors => {
  const errors: TradingBotFormErrors = {}

  // Validate each field
  errors.tradeCapital = validateTradeCapital(formData.tradeCapital)
  errors.targetProfit = validateTargetProfit(formData.targetProfit)
  errors.tradeAmount = validateTradeAmount(formData.tradeAmount, formData.tradeCapital)
  errors.tradeExpiryDuration = validateTradeExpiryDuration(formData.tradeExpiryDuration)

  return errors
}

/**
 * Checks if the form has any validation errors
 */
export const hasValidationErrors = (errors: TradingBotFormErrors): boolean => {
  return Object.values(errors).some((error) => error !== undefined && error !== '')
}

/**
 * Checks if the form is completely valid
 */
export const isFormValid = (formData: TradingBotFormData): boolean => {
  const errors = validateTradingBotForm(formData)
  return !hasValidationErrors(errors)
}

/**
 * Formats a numeric value for display
 */
export const formatNumericValue = (value: string | number): string => {
  if (typeof value === 'string') {
    const numValue = parseFloat(value)
    if (isNaN(numValue)) {
      return value
    }
    return numValue.toFixed(2)
  }
  return value.toFixed(2)
}

/**
 * Sanitizes numeric input to prevent invalid characters
 */
export const sanitizeNumericInput = (value: string): string => {
  // Remove any non-numeric characters except decimal point and minus sign
  return value.replace(/[^0-9.-]/g, '')
}

/**
 * Validates that a numeric input is within reasonable bounds for currency
 */
export const isValidCurrencyAmount = (value: string): boolean => {
  const numValue = parseFloat(value)
  if (isNaN(numValue)) {
    return false
  }

  // Check for reasonable currency bounds
  return numValue >= 0.01 && numValue <= 9999999.99
}

/**
 * Gets field-specific validation constraints
 */
export const getFieldConstraints = (fieldName: keyof typeof VALIDATION_CONSTRAINTS) => {
  return VALIDATION_CONSTRAINTS[fieldName]
}

/**
 * Gets field-specific error messages
 */
export const getFieldErrorMessages = (fieldName: keyof typeof ERROR_MESSAGES) => {
  return ERROR_MESSAGES[fieldName]
}
